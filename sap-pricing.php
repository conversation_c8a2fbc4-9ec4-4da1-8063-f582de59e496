<?php
/**
 * Plugin Name: SAP Pricing API
 * Description: Processes SAP pricing data and updates WooCommerce product prices
 * Version: 1.0
 * Author: ATAK Interactive
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Register REST route
add_action( 'rest_api_init', function() {
    register_rest_route( 'wc/v3', '/sap-pricing', [
        'methods'             => 'POST',
        'callback'            => 'sap_process_pricing',
        'permission_callback' => 'sap_pricing_permission_check',
    ] );
    
    // Test endpoint
    register_rest_route( 'wc/v3', '/sap-pricing-test', [
        'methods'             => 'GET',
        'callback'            => function() {
            return rest_ensure_response([
                'status' => 'working',
                'message' => 'SAP Pricing API is active',
                'timestamp' => current_time( 'mysql' ),
                'auth_required' => 'yes'
            ]);
        },
        'permission_callback' => '__return_true',
    ] );

    // Debug endpoint to inspect product pricing meta
    register_rest_route( 'wc/v3', '/sap-pricing-debug', [
        'methods'             => 'POST',
        'callback'            => function( WP_REST_Request $request ) {
            $data = $request->get_json_params();
            if ( empty( $data['product_id'] ) ) {
                return new WP_Error( 'missing_product_id', 'product_id is required', [ 'status' => 400 ] );
            }

            $product_id = (int) $data['product_id'];
            $pricing_data = get_product_pricing_data( $product_id );

            // Get all meta to see what's actually stored
            $all_meta = get_post_meta( $product_id );
            $filtered_meta = [];
            foreach ( $all_meta as $key => $value ) {
                $filtered_meta[$key] = is_array( $value ) && count( $value ) === 1 ? $value[0] : $value;
            }

            return rest_ensure_response([
                'product_id' => $product_id,
                'current_pricing_data' => $pricing_data,
                'all_meta' => $filtered_meta,
                'product_title' => get_the_title( $product_id ),
                'product_type' => get_post_type( $product_id )
            ]);
        },
        'permission_callback' => '__return_true',
    ] );
} );

/**
 * Permission callback for SAP Pricing API
 * Uses WordPress REST API authentication (Basic Auth or Application Passwords)
 */
function sap_pricing_permission_check( WP_REST_Request $request ) {
    error_log( "🔍 SAP Pricing API: Checking WordPress REST API authentication" );
    
    // Get the current user (WordPress handles authentication automatically)
    $user = wp_get_current_user();
    
    if ( ! $user || ! $user->ID ) {
        error_log( "❌ SAP Pricing API: No authenticated user found" );
        return new WP_Error( 'rest_not_logged_in', 'You are not currently logged in.', [ 'status' => 401 ] );
    }
    
    error_log( "🔍 SAP Pricing API: Authenticated user: {$user->ID} ({$user->user_login})" );
    
    // Check if user has appropriate capabilities
    $required_capabilities = [
        'manage_options',       // Administrator capability
        'manage_woocommerce',   // WooCommerce admin
        'edit_shop_orders',     // WooCommerce orders
        'edit_users',           // User management
        'edit_products',        // Product management
    ];

    foreach ( $required_capabilities as $cap ) {
        if ( user_can( $user, $cap ) ) {
            error_log( "✅ SAP Pricing API: User has capability: {$cap}" );
            return true;
        }
    }

    // Check for specific roles
    $user_roles = $user->roles ?? [];
    $allowed_roles = [ 'administrator', 'shop_manager', 'editor' ];
    
    foreach ( $allowed_roles as $role ) {
        if ( in_array( $role, $user_roles ) ) {
            error_log( "✅ SAP Pricing API: User has allowed role: {$role}" );
            return true;
        }
    }

    error_log( "❌ SAP Pricing API: User lacks sufficient permissions. Roles: " . implode( ', ', $user_roles ) );
    return new WP_Error( 'rest_forbidden', 'Sorry, you are not allowed to access this endpoint.', [ 'status' => 403 ] );
}

/**
 * Main endpoint to process SAP pricing data
 */
function sap_process_pricing( WP_REST_Request $request ) {
    $start_time = microtime( true );
    $request_id = uniqid( 'sap_pricing_' );

    // Log API call start with comprehensive details
    sap_pricing_log_api( "🚀 SAP-Pricing API CALL START [ID: {$request_id}]" );
    sap_pricing_log_api( "📡 Request Method: " . $request->get_method() );
    sap_pricing_log_api( "📡 Request URL: " . $request->get_route() );
    sap_pricing_log_api( "📡 Request Time: " . current_time( 'mysql' ) );
    sap_pricing_log_api( "📡 User Agent: " . ( $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown' ) );
    sap_pricing_log_api( "📡 Client IP: " . sap_pricing_get_client_ip() );

    // Log authentication details
    $current_user = wp_get_current_user();
    if ( $current_user && $current_user->ID ) {
        sap_pricing_log_api( "🔐 Authenticated User: {$current_user->ID} ({$current_user->user_login})" );
    }

    $data = $request->get_json_params();

    // Log the incoming data (sanitized for security)
    $sanitized_data = sap_pricing_sanitize_log_data( $data );
    sap_pricing_log_api( "📥 SAP-Pricing Request Data [ID: {$request_id}]: " . wp_json_encode( $sanitized_data, JSON_PRETTY_PRINT ) );

    // Validation
    if ( empty( $data ) || ! is_array( $data ) ) {
        error_log( "❌ Invalid data structure" );
        return new WP_Error( 'invalid_data', 'Invalid data structure. Expected JSON object.', [ 'status' => 400 ] );
    }

    // Check for pricing array
    if ( ! isset( $data['pricing'] ) || ! is_array( $data['pricing'] ) ) {
        error_log( "❌ Invalid data structure - missing pricing array" );
        return new WP_Error( 'invalid_data', 'Invalid data structure. Expected "pricing" array.', [ 'status' => 400 ] );
    }

    $pricing_items = $data['pricing'];
    sap_pricing_log_api( "📦 SAP-Pricing Processing batch of " . count( $pricing_items ) . " materials [ID: {$request_id}]" );

    // Process each pricing item
    $processed_materials = [];
    $total_errors = [];
    $total_updated_currencies = 0;

    foreach ( $pricing_items as $index => $pricing_item ) {
        $material_result = process_single_pricing_item( $pricing_item, $index, $request_id );

        if ( is_wp_error( $material_result ) ) {
            $total_errors[] = "Item {$index}: " . $material_result->get_error_message();
            error_log( "❌ Error processing pricing item {$index}: " . $material_result->get_error_message() );
        } else {
            $processed_materials[] = $material_result;
            $total_updated_currencies += $material_result['total_currencies_updated'];
            error_log( "✅ Successfully processed material: {$material_result['material_number']}" );
        }
    }

    // Build response
    $response = [
        'success' => true,
        'total_materials_processed' => count( $processed_materials ),
        'total_materials_requested' => count( $pricing_items ),
        'total_currencies_updated' => $total_updated_currencies,
        'processed_materials' => $processed_materials,
        'errors' => $total_errors
    ];

    if ( ! empty( $total_errors ) ) {
        $response['partial_success'] = true;
        $response['success'] = count( $processed_materials ) > 0; // Success if at least one material was processed
    }

    // Log API call completion
    $end_time = microtime( true );
    $execution_time = round( ( $end_time - $start_time ) * 1000, 2 ); // Convert to milliseconds

    sap_pricing_log_api( "📤 SAP-Pricing Response [ID: {$request_id}]: " . wp_json_encode( $response, JSON_PRETTY_PRINT ) );
    sap_pricing_log_api( "⏱️ SAP-Pricing API CALL COMPLETE [ID: {$request_id}] - Execution Time: {$execution_time}ms" );
    sap_pricing_log_api( "✅ SAP-Pricing Batch Success: {$response['success']}, Materials Processed: {$response['total_materials_processed']}/{$response['total_materials_requested']}, Total Currencies Updated: {$response['total_currencies_updated']}" );

    return rest_ensure_response( $response );
}

/**
 * Process a single pricing item
 */
function process_single_pricing_item( $pricing_item, $index, $request_id ) {
    // Validate required fields for this pricing item
    if ( empty( $pricing_item['materialNumber'] ) ) {
        error_log( "❌ Missing materialNumber for item {$index}" );
        return new WP_Error( 'missing_material_number', "materialNumber is required for item {$index}", [ 'status' => 400 ] );
    }

    if ( empty( $pricing_item['price'] ) || ! is_array( $pricing_item['price'] ) ) {
        error_log( "❌ Missing or invalid price data for item {$index}" );
        return new WP_Error( 'missing_price_data', "price data is required and must be an object for item {$index}", [ 'status' => 400 ] );
    }

    $material_number = sanitize_text_field( $pricing_item['materialNumber'] );

    sap_pricing_log_api( "🔍 SAP-Pricing Processing material {$material_number} [ID: {$request_id}, Item: {$index}]" );

    // Find product by material number (SKU)
    $product_id = find_product_by_sku( $material_number );

    if ( ! $product_id ) {
        error_log( "❌ Product not found for material number: {$material_number}" );
        sap_pricing_log_api( "❌ SAP-Pricing Product not found: {$material_number} [ID: {$request_id}, Item: {$index}]" );
        return new WP_Error( 'product_not_found', "Product not found for materialNumber: {$material_number}", [ 'status' => 404 ] );
    }

    error_log( "🔍 Found product ID {$product_id} for material number {$material_number}" );
    sap_pricing_log_api( "✅ SAP-Pricing Found product ID {$product_id} for material {$material_number} [ID: {$request_id}, Item: {$index}]" );

    // Process pricing data for each currency
    $updated_currencies = [];
    $errors = [];

    foreach ( $pricing_item['price'] as $currency => $price_data ) {
        $result = update_product_price_for_currency( $product_id, $currency, $price_data );

        if ( is_wp_error( $result ) ) {
            $errors[] = "Currency {$currency}: " . $result->get_error_message();
            error_log( "❌ Error updating price for currency {$currency}: " . $result->get_error_message() );
            sap_pricing_log_api( "❌ SAP-Pricing Currency update failed: {$material_number} - {$currency} - " . $result->get_error_message() . " [ID: {$request_id}, Item: {$index}]" );
        } else {
            $updated_currencies[] = $result;
            error_log( "✅ Successfully updated price for currency {$currency}" );
            sap_pricing_log_api( "✅ SAP-Pricing Currency updated: {$material_number} - {$currency} - Price: {$result['price']} [ID: {$request_id}, Item: {$index}]" );
        }
    }

    // Return result for this material
    $material_result = [
        'product_id' => $product_id,
        'material_number' => $material_number,
        'updated_currencies' => $updated_currencies,
        'total_currencies_updated' => count( $updated_currencies ),
        'errors' => $errors
    ];

    if ( ! empty( $errors ) ) {
        $material_result['partial_success'] = true;
    }

    return $material_result;
}

/**
 * Find WooCommerce product by SKU
 */
function find_product_by_sku( $sku ) {
    // Search for product by SKU
    $product_id = wc_get_product_id_by_sku( $sku );

    if ( $product_id ) {
        return $product_id;
    }

    // Fallback: search using WP_Query
    $query = new WP_Query([
        'post_type' => 'product',
        'post_status' => 'publish',
        'posts_per_page' => 1,
        'fields' => 'ids',
        'meta_query' => [
            [
                'key' => '_sku',
                'value' => $sku,
                'compare' => '='
            ]
        ]
    ]);

    if ( $query->have_posts() ) {
        return $query->posts[0];
    }

    return false;
}

/**
 * Update product price for a specific currency
 */
function update_product_price_for_currency( $product_id, $currency, $price_data ) {
    // Validate price data
    if ( ! isset( $price_data['price'] ) ) {
        return new WP_Error( 'missing_price', 'price is required for price data' );
    }

    $price = (float) $price_data['price'];
    $price_code = isset( $price_data['price_code'] ) ? sanitize_text_field( $price_data['price_code'] ) : '';

    // Map currency to custom field names
    $currency_upper = strtoupper( $currency );
    $price_meta_key = '';
    $price_code_meta_key = '';

    switch ( $currency_upper ) {
        case 'US':
        case 'USD':
            $price_meta_key = 'custom_usd_price';
            $price_code_meta_key = 'usd_price_code';
            break;
        case 'EU':
        case 'EUR':
            $price_meta_key = 'custom_eur_price';
            $price_code_meta_key = 'eur_price_code';
            break;
        case 'GB':
        case 'GBP':
            $price_meta_key = 'custom_gbp_price';
            $price_code_meta_key = 'gbp_price_code';
            break;
        default:
            return new WP_Error( 'unsupported_currency', "Unsupported currency: {$currency}" );
    }

    error_log( "🔍 Updating product {$product_id} - Currency: {$currency}, Price: {$price}, Price Code: {$price_code}" );

    // Update price for currency
    $price_result = update_post_meta( $product_id, $price_meta_key, $price );
    if ( $price_result === false ) {
        error_log( "❌ Failed to update price meta {$price_meta_key} for product {$product_id}" );
        return new WP_Error( 'price_update_failed', "Failed to update price for currency {$currency}" );
    }
    error_log( "✅ Updated price meta {$price_meta_key} = {$price}" );

    // Update price code if provided
    $price_code_updated = true;
    if ( ! empty( $price_code ) ) {
        $price_code_result = update_post_meta( $product_id, $price_code_meta_key, $price_code );
        if ( $price_code_result === false ) {
            error_log( "⚠️ Failed to update price code meta {$price_code_meta_key} for product {$product_id}" );
            $price_code_updated = false;
            // Don't return error - continue with price update success
        } else {
            error_log( "✅ Updated price code meta {$price_code_meta_key} = {$price_code}" );
        }
    }

    // Verify the updates
    $final_price = get_post_meta( $product_id, $price_meta_key, true );
    $final_price_code = get_post_meta( $product_id, $price_code_meta_key, true );
    error_log( "🔍 Final verification - Price: {$final_price}, Price Code: {$final_price_code}" );

    // Clear any relevant caches
    wp_cache_delete( $product_id, 'post_meta' );

    // If this is a WooCommerce product, clear WC caches too
    if ( function_exists( 'wc_delete_product_transients' ) ) {
        wc_delete_product_transients( $product_id );
    }

    error_log( "✅ Updated product {$product_id} price for currency {$currency}: {$price}, price code: {$price_code}" );

    return [
        'currency' => $currency,
        'price' => $price,
        'price_code' => $price_code,
        'price_meta_key' => $price_meta_key,
        'price_code_meta_key' => $price_code_meta_key,
        'price_code_updated' => $price_code_updated,
        'final_price_value' => $final_price,
        'final_price_code_value' => $final_price_code
    ];
}

/**
 * Helper function to get current pricing data for a product (for debugging)
 */
function get_product_pricing_data( $product_id ) {
    $pricing_data = [];

    // Define the pricing meta keys
    $pricing_meta_keys = [
        'custom_usd_price',
        'custom_eur_price',
        'custom_gbp_price',
        'usd_price_code',
        'eur_price_code',
        'gbp_price_code'
    ];

    foreach ( $pricing_meta_keys as $key ) {
        $value = get_post_meta( $product_id, $key, true );
        if ( $value !== '' ) {
            $pricing_data[$key] = $value;
        }
    }

    return $pricing_data;
}

/**
 * Write to custom API log file
 */
function sap_pricing_log_api( $message ) {
    $log_dir = WP_CONTENT_DIR . '/logs';
    $log_file = $log_dir . '/APIlogs.log';

    // Create logs directory if it doesn't exist
    if ( ! file_exists( $log_dir ) ) {
        wp_mkdir_p( $log_dir );
    }

    // Format log entry with timestamp
    $timestamp = current_time( 'Y-m-d H:i:s' );
    $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;

    // Write to log file
    file_put_contents( $log_file, $log_entry, FILE_APPEND | LOCK_EX );

    // Also log to WordPress error log for backup (optional)
    error_log( $message );
}

/**
 * Get client IP address for logging
 */
function sap_pricing_get_client_ip() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

    foreach ( $ip_keys as $key ) {
        if ( ! empty( $_SERVER[$key] ) ) {
            $ip = sanitize_text_field( $_SERVER[$key] );
            // Handle comma-separated IPs (from proxies)
            if ( strpos( $ip, ',' ) !== false ) {
                $ip = trim( explode( ',', $ip )[0] );
            }
            if ( filter_var( $ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE ) ) {
                return $ip;
            }
        }
    }

    return $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
}

/**
 * Sanitize data for logging (remove sensitive information)
 */
function sap_pricing_sanitize_log_data( $data ) {
    if ( ! is_array( $data ) ) {
        return $data;
    }

    $sanitized = $data;

    // Remove or mask sensitive fields
    $sensitive_fields = ['password', 'token', 'secret', 'key'];

    array_walk_recursive( $sanitized, function( &$value, $key ) use ( $sensitive_fields ) {
        if ( in_array( strtolower( $key ), $sensitive_fields ) ) {
            $value = '[REDACTED]';
        }
    });

    return $sanitized;
}
